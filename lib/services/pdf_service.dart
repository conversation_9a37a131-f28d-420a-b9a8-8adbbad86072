import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import 'package:file_picker/file_picker.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PdfService {
  /// التحقق من الأذونات المطلوبة حسب إصدار Android
  Future<bool> _requestStoragePermissions() async {
    if (!Platform.isAndroid) {
      return true; // iOS لا يحتاج أذونات خاصة
    }

    try {
      // التحقق من إصدار Android
      final isAndroid13Plus = await _isAndroid13OrHigher();
      
      if (isAndroid13Plus) {
        // Android 13+ يستخدم Storage Access Framework
        // لا نحتاج أذونات خاصة - file_picker سيتعامل مع كل شيء
        print('Android 13+ detected - using Storage Access Framework');
        return true;
      } else {
        // Android 12 وما دون - نحتاج READ_EXTERNAL_STORAGE فقط
        print('Android 12 or below - requesting storage permission');
        var status = await Permission.storage.status;
        
        if (!status.isGranted) {
          status = await Permission.storage.request();
        }
        
        if (status.isPermanentlyDenied) {
          print('Storage permission permanently denied');
          await openAppSettings();
          return false;
        }
        
        return status.isGranted;
      }
    } catch (e) {
      print('Error checking permissions: $e');
      // في حالة الخطأ، نجرب المتابعة
      return true;
    }
  }

  /// التحقق من إصدار Android
  Future<bool> _isAndroid13OrHigher() async {
    if (!Platform.isAndroid) return false;
    
    try {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // Android 13 = API 33
    } catch (e) {
      print('Error getting Android version: $e');
      return false;
    }
  }

  /// اختيار ملف PDF من الجهاز
  Future<Map<String, dynamic>?> pickPdfFile() async {
    try {
      print('Starting PDF file selection...');

      // 1. التحقق من الأذونات
      final hasPermission = await _requestStoragePermissions();
      if (!hasPermission) {
        throw Exception(
          'إذن الوصول إلى التخزين مطلوب لاختيار الملفات. '
          'يرجى منح الإذن من إعدادات التطبيق.'
        );
      }

      print('Permissions granted, opening file picker...');

      // 2. فتح منتقي الملفات
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        dialogTitle: 'اختر ملف PDF',
        // إعدادات إضافية لـ Android
        withData: false, // تحسين الأداء
        withReadStream: false,
      );

      if (result == null || result.files.isEmpty) {
        print('User cancelled file selection');
        return null;
      }

      final platformFile = result.files.first;
      final filePath = platformFile.path;

      if (filePath == null) {
        print('Error: File path is null');
        throw Exception(
          'لم يتم الحصول على مسار الملف. يرجى المحاولة مرة أخرى.'
        );
      }

      print('Selected file path: $filePath');

      // 3. التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        print('Error: File does not exist at path: $filePath');
        throw Exception(
          'لم يتم العثور على الملف المحدد. يرجى المحاولة مرة أخرى.'
        );
      }

      // 4. التحقق من حجم الملف
      final fileSize = await file.length();
      final fileSizeInMB = fileSize / (1024 * 1024);
      print('File size: ${fileSize} bytes (${fileSizeInMB.toStringAsFixed(2)} MB)');

      // حد أقصى 100 ميجابايت مع تحذير للملفات الكبيرة
      if (fileSize > 100 * 1024 * 1024) {
        throw Exception(
          'حجم الملف كبير جداً (${fileSizeInMB.toStringAsFixed(1)} ميجابايت). '
          'يرجى اختيار ملف أصغر من 100 ميجابايت.'
        );
      }

      // تحذير للملفات الكبيرة (أكبر من 20 ميجابايت)
      bool isLargeFile = fileSizeInMB > 20;
      if (isLargeFile) {
        print('Warning: Large file detected (${fileSizeInMB.toStringAsFixed(2)} MB)');
      }

      print('File validation successful');
      return {
        'file': file,
        'fileName': platformFile.name,
        'fileSize': fileSize,
      };

    } catch (e) {
      print('Error in pickPdfFile: $e');
      
      // إعادة رمي الأخطاء المعروفة
      if (e is Exception) {
        rethrow;
      }
      
      // خطأ عام
      throw Exception(
        'فشل في اختيار ملف PDF. يرجى التأكد من اختيار ملف صالح.'
      );
    }
  }
  
  /// استخراج النص من ملف PDF مع معالجة محسنة للملفات الكبيرة
  Future<String> extractTextFromPdf(File file, {Function(String)? onProgress}) async {
    PdfDocument? document;
    try {
      onProgress?.call('بدء استخراج النص من PDF...');

      // التحقق من وجود الملف
      if (!await file.exists()) {
        print('Error: PDF file does not exist at path: ${file.path}');
        throw Exception('ملف PDF المحدد غير موجود.');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      final fileSizeInMB = fileSize / (1024 * 1024);
      print('Processing PDF file: ${fileSizeInMB.toStringAsFixed(2)} MB');

      onProgress?.call('تحليل ملف PDF (${fileSizeInMB.toStringAsFixed(1)} ميجابايت)...');

      // قراءة بيانات الملف بطريقة محسنة للملفات الكبيرة
      onProgress?.call('قراءة بيانات الملف...');
      Uint8List bytes;

      try {
        bytes = await file.readAsBytes();
      } catch (e) {
        throw Exception('فشل في قراءة الملف. قد يكون الملف كبير جداً أو تالف.');
      }

      if (bytes.isEmpty) {
        throw Exception('الملف فارغ أو تالف.');
      }

      // إنشاء مستند PDF مع معالجة الأخطاء
      onProgress?.call('تحليل مستند PDF...');
      try {
        document = PdfDocument(inputBytes: bytes);
      } catch (e) {
        throw Exception('فشل في تحليل ملف PDF. قد يكون الملف تالف أو محمي بكلمة مرور.');
      }

      if (document.pages.count == 0) {
        throw Exception('ملف PDF لا يحتوي على صفحات.');
      }

      onProgress?.call('تم العثور على ${document.pages.count} صفحة');

      // استخراج النص مع معالجة محسنة للملفات الكبيرة
      final extractor = PdfTextExtractor(document);
      final buffer = StringBuffer();

      // تحديد عدد الصفحات المراد معالجتها (حد أقصى للملفات الكبيرة)
      int maxPagesToProcess = document.pages.count;
      if (fileSizeInMB > 30) {
        // للملفات الكبيرة، معالجة أول 50 صفحة فقط
        maxPagesToProcess = math.min(50, document.pages.count);
        onProgress?.call('ملف كبير - سيتم معالجة أول $maxPagesToProcess صفحة');
      } else if (fileSizeInMB > 10) {
        // للملفات المتوسطة، معالجة أول 100 صفحة
        maxPagesToProcess = math.min(100, document.pages.count);
        onProgress?.call('ملف متوسط - سيتم معالجة أول $maxPagesToProcess صفحة');
      } else {
        onProgress?.call('بدء استخراج النص من $maxPagesToProcess صفحة');
      }

      // استخراج النص من الصفحات مع معالجة الأخطاء
      int successfulPages = 0;
      int failedPages = 0;

      for (int i = 0; i < maxPagesToProcess; i++) {
        try {
          final pageText = extractor.extractText(
            startPageIndex: i,
            endPageIndex: i
          );

          if (pageText.trim().isNotEmpty) {
            buffer.write(pageText.trim());
            buffer.write('\n\n');
            successfulPages++;
          }

          // تحديث التقدم كل 5 صفحات أو للصفحات الأولى
          if ((i + 1) % 5 == 0 || i < 10) {
            final progress = ((i + 1) / maxPagesToProcess * 100).round();
            onProgress?.call('معالجة الصفحات: ${i + 1}/$maxPagesToProcess ($progress%)');
          }

          // فحص طول النص لتجنب استهلاك الذاكرة المفرط
          if (buffer.length > 30000) {
            onProgress?.call('تم الوصول للحد الأقصى من النص - إيقاف المعالجة');
            break;
          }

          // إضافة تأخير صغير للسماح للواجهة بالتحديث
          if (i % 10 == 0) {
            await Future.delayed(Duration(milliseconds: 10));
          }

        } catch (e) {
          print('Error extracting text from page ${i + 1}: $e');
          failedPages++;
          // متابعة مع الصفحات الأخرى
        }
      }

      onProgress?.call('تم استخراج النص من $successfulPages صفحة بنجاح');

      String extractedText = buffer.toString().trim();

      if (extractedText.isEmpty) {
        throw Exception(
          'لم يتم العثور على نص في ملف PDF. '
          'قد يكون الملف يحتوي على صور فقط أو محمي بكلمة مرور.'
        );
      }

      // تحديد طول النص لتجنب قيود واجهة برمجة التطبيقات
      final originalLength = extractedText.length;
      if (extractedText.length > 20000) {
        onProgress?.call('تحسين النص للمعالجة...');
        extractedText = '${extractedText.substring(0, 20000)}\n\n[تم اقتطاع النص للحصول على أفضل النتائج...]';
      }

      onProgress?.call('تم الانتهاء - النص جاهز (${extractedText.length} حرف)');
      return extractedText;

    } catch (e) {
      print('Error in extractTextFromPdf: $e');

      if (e is Exception) {
        rethrow;
      }

      throw Exception('فشل في استخراج النص من ملف PDF: ${e.toString()}');
    } finally {
      // تأكد من تحرير الموارد
      document?.dispose();
    }
  }
}
